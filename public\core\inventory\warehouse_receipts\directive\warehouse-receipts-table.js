warehouseReceipts.directive("warehouseReceiptsTable", function () {
  return {
    restrict: "E",
    scope: {
      warehouse_id: "=warehouseId",
      purchase_order_id: "=purchaseOrderId",
      is_purchase_order: "=isPurchaseOrder",
      is_merchandise: "=isMerchandise",
      hide_customer: "=hideCustomer",
      isPallet: "=isPallet",
      itemMigrationId: "=itemMigrationId",
      voyageScheduleId: "=voyageScheduleId",
      salesOrderReturnId: "=salesOrderReturnId",
      addRoute: "=addRoute",
      detailRoute: "=detailRoute",
      editRoute: "=editRoute",
      is_tire: "=isTire",
      addParams: "=addParams",
      is_vulkanisir: "=isVulkanisir",
    },
    require: "ngModel",
    templateUrl:
      "/core/inventory/warehouse_receipts/view/warehouse-receipts-table.html",
    controller: function (
      $scope,
      $http,
      $attrs,
      $rootScope,
      $compile,
      $filter,
      $timeout,
      $state
    ) {
      $(".ibox-content").addClass("sk-loading");
      $scope.is_admin = authUser.is_admin;

      $scope.delete = function (ids) {
        var cfs = confirm("Apakah Anda Yakin?");
        if (cfs) {
          $http.delete(baseUrl + "/operational_warehouse/receipt/" + ids).then(
            function (data) {
              toastr.success("Data berhasil dihapus", "Selamat !");
              oTable.ajax.reload();
            },
            function (error) {
              $scope.disBtn = false;
              if (error.status == 422) {
                var det = "";
                angular.forEach(error.data.errors, function (val, i) {
                  det += "- " + val + "<br>";
                });
                toastr.warning(det, error.data.message);
              } else {
                toastr.error(error.data.message, "Error Has Found !");
              }
            }
          );
        }
      };

      $scope.formData = {};

      if ($scope.itemMigrationId) {
        $scope.is_admin = authUser.is_admin;
        $scope.is_multibranch = authUser.is_multibranch;
        let warehouse_id = null;
      
        $scope.showWarehouse = async function (company_id) {
          try {
            let response = await $http.get(
              baseUrl + "/inventory/warehouse?company_id=" + company_id
            );
            return response.data.data.length > 0 ? response.data.data[0].id : null;
          } catch (error) {
            console.error("Error fetching warehouse data:", error);
            return null;
          }
        };
      
        async function fetchData() {
          let company_id = parseInt(authUser.company_id);

          warehouse_id = await $scope.showWarehouse(company_id);

          console.log('=== DEBUG fetchData ===');
          console.log('company_id:', company_id);
          console.log('warehouse_id from showWarehouse:', warehouse_id);
          console.log('itemMigrationId:', $scope.itemMigrationId);

          $http
            .get(
              baseUrl + "/operational_warehouse/mutasi_transfer/" + $scope.itemMigrationId
            )
            .then(function (data) {
              $scope.mutasitransfer = data.data.item;
              $scope.detailMutasiTransfer = data.data.detail;

              console.log('mutasitransfer:', $scope.mutasitransfer);
              console.log('detailMutasiTransfer:', $scope.detailMutasiTransfer);
              console.log('mutasitransfer.status:', $scope.mutasitransfer.status);

              if ($scope.mutasitransfer.status == 1) {
                $scope.hide_add = true;
                console.log('hide_add set to TRUE - status is 1 (approved)');
              } else if (
                warehouse_id !== null &&
                $scope.detailMutasiTransfer.some(
                  (item) => item.destination_warehouse_id != warehouse_id
                )
              ) {
                $scope.hide_add = true;
                console.log('hide_add set to TRUE - warehouse mismatch');
                console.log('User warehouse_id:', warehouse_id);
                $scope.detailMutasiTransfer.forEach((item, index) => {
                  console.log(`Detail ${index} destination_warehouse_id:`, item.destination_warehouse_id);
                });
              } else {
                $scope.hide_add = false;
                console.log('hide_add set to FALSE - all conditions passed');
              }

              console.log('Final hide_add value after fetchData:', $scope.hide_add);
              console.log('=== END DEBUG fetchData ===');
            })
            .catch(function (error) {
              console.log("Error fetching mutasi transfer:", error);
            });
        }
      
        fetchData();
      }

      var columnDefs = [
        { title: "" },
        { title: $rootScope.solog.label.general.branch },
        { title: $rootScope.solog.label.general.warehouse },
        { title: "No GR" },
        { title: "No PO" },
      ];
      
      if (!$scope.hide_customer) {
        columnDefs.push({ title: $rootScope.solog.label.general.customer });
      }
      
      columnDefs = columnDefs.concat([
        { title: $rootScope.solog.label.general.receive_date },
        // { "title" : "Selesai Stripping" },
        { title: $rootScope.solog.label.general.total_item },
        { title: "Total Price" },
        { title: $rootScope.solog.label.general.status },
      ]);

      columnDefs = columnDefs.map((c, i) => {
        c.targets = i;
        return c;
      });

      var columns = [
        {
          data: null,
          searchable: false,
          orderable: false,
          className: "text-center",
          render: function (item) {
            item.action = null;
            var html = "";
            if (item.status == 0 || item.status == 2) {
              html +=
                '<span><warehouse-receipts-approve-button on-submit="searchData()" id="' +
                item.id +
                '" /></span>&nbsp;&nbsp;';
            }

            html +=
              '<a title="Detail" ng-show="$root.roleList.includes(\'inventory.receipt.detail\')" ng-click="show(' +
              item.id +
              ')"><i class="fa fa-folder-o"></i></a>&nbsp;&nbsp;';

            if (item.status == 0 || item.status == 2) {
              if ($scope.editRoute) {
                html +=
                  '<span><warehouse-receipts-edit-button id="' +
                  item.id +
                  '" edit-route="\'' +
                  $scope.editRoute +
                  "'\" /></span>";
              } else {
                html +=
                  '<span><warehouse-receipts-edit-button id="' +
                  item.id +
                  '" /></span>';
              }
            }

            if (item.status != 1) {
              html +=
                "&nbsp;&nbsp<a ng-click='delete(" +
                item.id +
                ")' data-toggle='tooltip' title='Hapus'><span class='fa fa-trash'></span>&nbsp;&nbsp;</a>";
            }

            return html;
          },
        },
        {
          data: "company.name",
          name: "company.name",
        },
        {
          data: "warehouse.name",
          name: "warehouse.name",
        },
         {
          data: null,
          name: "code",
          className: "font-bold",
          render: function (resp) {
            if ($rootScope.roleList.includes("inventory.receipt.detail")) {
              return (
                '<a ng-click="show(' + resp.id + ')">' + resp.code + "</a>"
              );
            } else {
              return resp.code;
            }
          },
        },
        {
          data: "no_po",
          name: "purchase_orders.code",
        },
        
      ];

      if (!$scope.hide_customer) {
        columns.push({
          data: "customer.name",
          name: "customer.name",
          className: "font-bold",
        });
      }

      

      columns = columns.concat([
        {
          data: null,
          name: "receive_date",
          searchable: false,
          render: function (resp) {
            var date = resp.receive_date.split(" ");
            return $filter("fullDate")(date[0]) + " " + date[1];
          },
        },
        // {
        //   data: null,
        //   name : 'stripping_done',
        //   searchable: false,
        //   render: function (resp) {
        //     return $filter('fullDate')(resp.stripping_done)
        //   }
        // },
        {
          data: "total",
          name: "det.total",
          className: "text-right",
        },
        {
          data: "total_pod",
          name: "pod.total_pod",
          className: "text-right"
        },
        {
          data: null,
          name: "status",
          className: "text-center",
          render: function (resp) {
            var status =
              resp.status == 0
                ? "Draft"
                : resp.status == 1
                ? "Disetujui"
                : "Dibatalkan";
            var className =
              resp.status == 0
                ? "badge-warning"
                : resp.status == 1
                ? "badge-primary"
                : "badge-danger";
            var outp =
              "<span class='badge " + className + "'>" + status + "</span>";

            return outp;
          },
        },
      ]);

      oTable = $("#datatable").DataTable({
        processing: true,
        serverSide: true,
        scrollX: false,
        order: [
          [4, "desc"],
          [0, "desc"],
        ],
        dom: "Blfrtip",
        lengthMenu: [
          [10, 25, 50, 100, -1],
          [10, 25, 50, 100, "All"],
        ],
        buttons: [
          {
            extend: "excel",
            enabled: true,
            action: newExportAction,
            text: '<span class="fa fa-file-excel-o"></span> Export Excel',
            className: "btn btn-default btn-sm pull-right",
            filename: "Penerimaan",
            messageTop: "Penerimaan",
            sheetName: "Data",
            title: "Penerimaan",
            exportOptions: {
              rows: {
                selected: true,
              },
            },
          },
        ],
        ajax: {
          headers: {
            Authorization: "Bearer " + authUser.api_token,
          },
          url:
            baseUrl + "/api/operational_warehouse/warehouse_receipt_datatable",
          dataSrc: function (d) {
            $(".ibox-content").removeClass("sk-loading");
            return d.data;
          },
          data: (e) => Object.assign(e, $scope.formData),
        },
        columnDefs: columnDefs,
        columns: columns,
        createdRow: function (row, data, dataIndex) {
          $(row).find("td").css("cursor", "context-menu");
          if (data.status == 0) {
            $(row).addClass("text-warning");
          } else if (data.status == 2) {
            $(row).addClass("text-danger");
          }
          $compile(angular.element(row).contents())($scope);
        },
      });

      $scope.$watch(function () {
        $scope.formData.purchase_order_id = $scope.purchase_order_id;
      });

      $compile($("thead"))($scope);

      oTable.buttons().container().appendTo("#export_button");

      $scope.show = function (id) {
        if ($scope.detailRoute) {
          $state.go($scope.detailRoute, { id: id });
        } else {
          $rootScope.insertBuffer();
          $state.go("operational_warehouse.receipt.show", { id: id });
        }
      };

      $scope.add = function () {
        // $rootScope.insertBuffer()
        if ($scope.addRoute) {
          $state.go($scope.addRoute, $scope.addParams);
        } else {
          $state.go("operational_warehouse.receipt.create");
        }
      };

      $scope.adjustField = function () {
        $scope.hide_warehouse = false;
        $scope.hide_add = false;
        $scope.hide_branch_filter = false;
        $scope.hide_customer_filter = false;

        console.log('=== DEBUG adjustField ===');
        console.log('warehouse_id:', $scope.warehouse_id);
        console.log('hideWarehouse attr:', $attrs.hideWarehouse);
        console.log('hideAdd attr:', $attrs.hideAdd);
        console.log('roleList length:', $rootScope.roleList ? $rootScope.roleList.length : 'undefined');
        console.log('roleList type:', typeof $rootScope.roleList);

        if ($scope.warehouse_id || $attrs.hideWarehouse) {
          $scope.hide_warehouse = true;
          // Don't hide add button for itemMigrationId case
          if (!$scope.itemMigrationId) {
            $scope.hide_add = true;
            console.log('hide_add set to TRUE by warehouse_id or hideWarehouse');
          } else {
            console.log('hide_add NOT set because itemMigrationId exists');
          }
        }
        if ($attrs.hideAdd) {
          $scope.hide_add = true;
          console.log('hide_add set to TRUE by hideAdd attribute');
        }
        if ($attrs.hideBranchFilter) {
          $scope.hide_branch_filter = true;
        }
        if ($attrs.hideCustomerFilter) {
          $scope.hide_customer_filter = true;
        }

        console.log('Final hide_add value:', $scope.hide_add);
        console.log('=== END DEBUG adjustField ===');
      };

      $scope.adjustField();

      $scope.searchData = function () {
        if ($scope.isPallet) {
          $scope.formData.is_pallet = 1;
        }
        if ($scope.is_purchase_order) {
          $scope.formData.is_purchase_order = 1;
        }
        if ($scope.is_merchandise) {
          $scope.formData.is_merchandise = $scope.is_merchandise;
        }
        if ($scope.itemMigrationId) {
          $scope.formData.item_migration_id = $scope.itemMigrationId;
        }
        if ($scope.voyageScheduleId) {
          $scope.formData.voyage_schedule_id = $scope.voyageScheduleId;
        }
        if ($scope.salesOrderReturnId) {
          $scope.formData.sales_order_return_id = $scope.salesOrderReturnId;
        }
        if ($scope.is_tire) {
          $scope.formData.is_tire = 1;
        }
        if ($scope.is_vulkanisir) {
          $scope.formData.is_vulkanisir = 1;
        }
        oTable.ajax.reload();
      };

      $scope.$watch("itemMigrationId", function () {
        $scope.searchData();
      });

      $scope.$watch("voyageScheduleId", function () {
        $scope.searchData();
      });

      $scope.resetFilter = function () {
        $scope.formData = {};
        $scope.searchData();
      };

      $scope.$on("reloadWarehouseReceipt", function (e, v) {
        $scope.searchData();
      });

      // Watch for roleList changes
      $scope.$watch(function() {
        return $rootScope.roleList;
      }, function(newVal, oldVal) {
        if (newVal && newVal.length > 0) {
          console.log('=== roleList loaded ===');
          console.log('roleList:', newVal);
          console.log('has inventory.receipt.create:', newVal.includes('inventory.receipt.create'));
          $scope.adjustField(); // Re-run adjustField when roles are loaded
        }
      });

      if ($attrs["warehouseId"]) {
        $scope.$watch("warehouse_id", function () {
          $scope.formData.warehouse_id = $scope.warehouse_id;
          $scope.searchData();
          $scope.adjustField();
        });
      }
    },
  };
});
